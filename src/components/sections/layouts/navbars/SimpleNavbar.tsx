"use client";

import { memo } from "react";
import Image from "next/image";
import SlideButton from "@/components/buttons/SlideButton";
import { SimpleNavbarStyle } from "../../styles/navbar/types";
import { getFunAndTrendySimpleNavbarStyle } from "../../styles/navbar/simple/funAndTrendy";
import { getFuturisticSimpleNavbarStyle } from "../../styles/navbar/simple/futuristicAndOutOfBox";
import { useSiteTheme, getThemeStyle } from "../../ThemeProvider";

interface SimpleNavbarProps {
  logoSrc?: string;
  logoAlt?: string;
  leftButtonText?: string;
  rightButtonText?: string;
  onLeftButtonClick?: () => void;
  onRightButtonClick?: () => void;
  className?: string;
  containerClassName?: string;
  logoClassName?: string;
  buttonClassName?: string;
  buttonContentClassName?: string;
  buttonBgColor?: string;
  buttonHoverBgColor?: string;
  buttonTextColor?: string;
  buttonHoverTextColor?: string;
}

function SimpleNavbar({
  logoSrc,
  logoAlt,
  leftButtonText,
  rightButtonText,
  onLeftButtonClick,
  onRightButtonClick,
  className = "",
  containerClassName = "",
  logoClassName = "",
  buttonClassName = "",
  buttonContentClassName = "",
  buttonBgColor,
  buttonHoverBgColor,
  buttonTextColor,
  buttonHoverTextColor,
}: SimpleNavbarProps) {
  const theme = useSiteTheme();
  const style: SimpleNavbarStyle = getThemeStyle(theme, {
    funAndTrendy: getFunAndTrendySimpleNavbarStyle,
    futuristicAndOutOfBox: getFuturisticSimpleNavbarStyle,
  });

  // Use style values as defaults, allow props to override
  const finalLogoSrc = logoSrc || style.navbar.logoSrc;
  const finalLogoAlt = logoAlt || style.navbar.logoAlt || "Logo";
  const finalLeftButtonText = leftButtonText || style.leftButton.text || "Menu";
  const finalRightButtonText =
    rightButtonText || style.rightButton.text || "Contact";
  return (
    <nav className={`${style.navbar.className} ${className}`}>
      <div
        className={`${style.navbar.containerClassName} ${containerClassName}`}
      >
        <SlideButton
          text={finalLeftButtonText}
          onClick={onLeftButtonClick}
          className={`${style.leftButton.buttonClassName} ${buttonClassName}`}
          contentClassName={`${style.leftButton.buttonContentClassName} ${buttonContentClassName}`}
          bgColor={buttonBgColor || style.leftButton.buttonBgColor}
          hoverBgColor={
            buttonHoverBgColor || style.leftButton.buttonHoverBgColor
          }
          textColor={buttonTextColor || style.leftButton.buttonTextColor}
          hoverTextColor={
            buttonHoverTextColor || style.leftButton.buttonHoverTextColor
          }
        />

        <div
          className={`absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 h-6 w-auto ${style.navbar.logoClassName} ${logoClassName}`}
        >
          <Image
            src={finalLogoSrc}
            alt={finalLogoAlt}
            height={32}
            width={120}
            className="h-full w-auto object-contain"
            priority
          />
        </div>

        <SlideButton
          text={finalRightButtonText}
          onClick={onRightButtonClick}
          className={`${style.rightButton.buttonClassName} ${buttonClassName}`}
          contentClassName={`${style.rightButton.buttonContentClassName} ${buttonContentClassName}`}
          bgColor={buttonBgColor || style.rightButton.buttonBgColor}
          hoverBgColor={
            buttonHoverBgColor || style.rightButton.buttonHoverBgColor
          }
          textColor={buttonTextColor || style.rightButton.buttonTextColor}
          hoverTextColor={
            buttonHoverTextColor || style.rightButton.buttonHoverTextColor
          }
        />
      </div>
    </nav>
  );
}

SimpleNavbar.displayName = "SimpleNavbar";

export default memo(SimpleNavbar);
