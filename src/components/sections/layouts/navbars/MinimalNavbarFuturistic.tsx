"use client";

import { memo } from "react";
import { SiteThemeProvider } from "../../ThemeProvider";
import MinimalNavbar from "./MinimalNavbar";

interface MinimalNavbarFuturisticProps {
  logoSrc?: string;
  logoWidth?: number;
  logoHeight?: number;
  logoClassName?: string;
  buttonText?: string;
  onButtonClick?: () => void;
  className?: string;
  buttonType?: string;
  buttonClassName?: string;
  buttonContentClassName?: string;
  buttonBgColor?: string;
  buttonHoverBgColor?: string;
  buttonTextColor?: string;
  buttonHoverTextColor?: string;
  colorTemplate?: 1 | 2;
}

function MinimalNavbarFuturistic({
  colorTemplate = 1,
  ...props
}: MinimalNavbarFuturisticProps) {
  return (
    <SiteThemeProvider
      theme={{
        styleVariant: "futuristicAndOutOfBox",
        colorTemplate,
        textAnimation: "slide",
      }}
    >
      <MinimalNavbar {...props} />
    </SiteThemeProvider>
  );
}

MinimalNavbarFuturistic.displayName = "MinimalNavbarFuturistic";

export default memo(MinimalNavbarFuturistic);
