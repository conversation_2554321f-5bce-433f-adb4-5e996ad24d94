"use client";
import React, { memo } from "react";
import Image from "next/image";
import StaggerButton from "../../../buttons/staggerButton/StaggerButton";
import SlideButton from "../../../buttons/SlideButton";
import { MinimalNavbarStyle } from "../../styles/navbar/types";
import { getFunAndTrendyMinimalNavbarStyle } from "../../styles/navbar/minimal/funAndTrendy";
import { getFuturisticMinimalNavbarStyle } from "../../styles/navbar/minimal/futuristicAndOutOfBox";
import { useSiteTheme, getThemeStyle } from "../../ThemeProvider";

/**
 * MinimalNavbar Component
 *
 * The button components (StaggerButton/SlideButton) can be replaced with any button component from @/components/buttons/
 *
 * @example Replace with different button:
 * Instead of StaggerButton or SlideButton, you can use:
 * - ArrowButton from '@/components/buttons/ArrowButton'
 * - BubbleButton from '@/components/buttons/BubbleButton'
 * - DirectionalHoverButton from '@/components/buttons/directionalHoverButton/DirectionalHoverButton'
 * - ExpandingButton from '@/components/buttons/ExpandingButton'
 * - MagneticButton from '@/components/buttons/magneticButton/MagneticButton'
 * - MovingBorderButton from '@/components/buttons/movingBorderButton/MovingBorderButton'
 * - PushableButton from '@/components/buttons/PushableButton'
 * - RotatingIconButton from '@/components/buttons/rotatingIconButton/RotatingIconButton'
 * - ShiftButton from '@/components/buttons/shiftButton/ShiftButton'
 * - UnderlineButton from '@/components/buttons/UnderlineButton'
 */
interface MinimalNavbarProps {
  logoSrc?: string;
  logoWidth?: number;
  logoHeight?: number;
  logoClassName?: string;
  buttonText?: string;
  onButtonClick?: () => void;
  className?: string;
  buttonType?: string;
  buttonClassName?: string;
  buttonContentClassName?: string;
  buttonBgColor?: string;
  buttonHoverBgColor?: string;
  buttonTextColor?: string;
  buttonHoverTextColor?: string;
}

const MinimalNavbar = memo<MinimalNavbarProps>(function MinimalNavbar({
  logoSrc,
  logoWidth,
  logoHeight,
  logoClassName = "",
  buttonText,
  onButtonClick = () => {},
  className = "",
  buttonType,
  buttonClassName = "",
  buttonContentClassName = "",
  buttonBgColor,
  buttonHoverBgColor,
  buttonTextColor,
  buttonHoverTextColor,
}) {
  const theme = useSiteTheme();
  const style: MinimalNavbarStyle = getThemeStyle(theme, {
    funAndTrendy: getFunAndTrendyMinimalNavbarStyle,
    futuristicAndOutOfBox: getFuturisticMinimalNavbarStyle,
  });

  // Use style values as defaults, allow props to override
  const finalLogoSrc = logoSrc || style.navbar.logoSrc;
  const finalLogoWidth = logoWidth || style.navbar.logoWidth || 120;
  const finalLogoHeight = logoHeight || style.navbar.logoHeight || 40;
  const finalButtonText = buttonText || style.button.text || "Join Now";
  const finalButtonType = buttonType || style.button.type || "stagger";
  return (
    <nav
      role="navigation"
      aria-label="Main navigation"
      className={`${style.navbar.className} ${className}`}
    >
      <Image
        src={finalLogoSrc}
        width={finalLogoWidth}
        height={finalLogoHeight}
        className={`h-[var(--text-xl)] w-auto ${style.navbar.logoClassName} ${logoClassName}`}
        alt="Company Logo"
        priority
      />

      {finalButtonType === "slide" ? (
        <SlideButton
          text={finalButtonText}
          onClick={onButtonClick}
          className={`${style.button.buttonClassName} ${buttonClassName}`}
          contentClassName={`${style.button.buttonContentClassName} ${buttonContentClassName}`}
          bgColor={buttonBgColor || style.button.buttonBgColor}
          hoverBgColor={buttonHoverBgColor || style.button.buttonHoverBgColor}
          textColor={buttonTextColor || style.button.buttonTextColor}
          hoverTextColor={
            buttonHoverTextColor || style.button.buttonHoverTextColor
          }
        />
      ) : (
        <StaggerButton
          text={finalButtonText}
          onClick={onButtonClick}
          className={`${style.button.buttonClassName} ${buttonClassName}`}
          bgClassName={style.button.buttonContentClassName}
          aria-label={finalButtonText}
        />
      )}
    </nav>
  );
});

export default MinimalNavbar;
