"use client";

import { memo } from "react";
import { SiteThemeProvider } from "../../ThemeProvider";
import MinimalNavbar from "./MinimalNavbar";

interface MinimalNavbarFunAndTrendyProps {
  logoSrc?: string;
  logoWidth?: number;
  logoHeight?: number;
  logoClassName?: string;
  buttonText?: string;
  onButtonClick?: () => void;
  className?: string;
  buttonType?: string;
  buttonClassName?: string;
  buttonContentClassName?: string;
  buttonBgColor?: string;
  buttonHoverBgColor?: string;
  buttonTextColor?: string;
  buttonHoverTextColor?: string;
  colorTemplate?: 1 | 2;
}

function MinimalNavbarFunAndTrendy({
  colorTemplate = 1,
  ...props
}: MinimalNavbarFunAndTrendyProps) {
  return (
    <SiteThemeProvider
      theme={{
        styleVariant: "funAndTrendy",
        colorTemplate,
        textAnimation: "slide",
      }}
    >
      <MinimalNavbar {...props} />
    </SiteThemeProvider>
  );
}

MinimalNavbarFunAndTrendy.displayName = "MinimalNavbarFunAndTrendy";

export default memo(MinimalNavbarFunAndTrendy);
