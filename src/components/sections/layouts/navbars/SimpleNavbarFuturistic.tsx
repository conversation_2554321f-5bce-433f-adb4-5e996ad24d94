"use client";

import { memo } from "react";
import { SiteThemeProvider } from "../../ThemeProvider";
import SimpleNavbar from "./SimpleNavbar";

interface SimpleNavbarFuturisticProps {
  logoSrc?: string;
  logoAlt?: string;
  leftButtonText?: string;
  rightButtonText?: string;
  onLeftButtonClick?: () => void;
  onRightButtonClick?: () => void;
  className?: string;
  containerClassName?: string;
  logoClassName?: string;
  buttonClassName?: string;
  buttonContentClassName?: string;
  buttonBgColor?: string;
  buttonHoverBgColor?: string;
  buttonTextColor?: string;
  buttonHoverTextColor?: string;
  colorTemplate?: 1 | 2;
}

function SimpleNavbarFuturistic({
  colorTemplate = 1,
  ...props
}: SimpleNavbarFuturisticProps) {
  return (
    <SiteThemeProvider
      theme={{
        styleVariant: "futuristicAndOutOfBox",
        colorTemplate,
        textAnimation: "slide",
      }}
    >
      <SimpleNavbar {...props} />
    </SiteThemeProvider>
  );
}

SimpleNavbarFuturistic.displayName = "SimpleNavbarFuturistic";

export default memo(SimpleNavbarFuturistic);
