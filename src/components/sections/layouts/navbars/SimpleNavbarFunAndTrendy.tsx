"use client";

import { memo } from "react";
import { SiteThemeProvider } from "../../ThemeProvider";
import SimpleNavbar from "./SimpleNavbar";

interface SimpleNavbarFunAndTrendyProps {
  logoSrc?: string;
  logoAlt?: string;
  leftButtonText?: string;
  rightButtonText?: string;
  onLeftButtonClick?: () => void;
  onRightButtonClick?: () => void;
  className?: string;
  containerClassName?: string;
  logoClassName?: string;
  buttonClassName?: string;
  buttonContentClassName?: string;
  buttonBgColor?: string;
  buttonHoverBgColor?: string;
  buttonTextColor?: string;
  buttonHoverTextColor?: string;
  colorTemplate?: 1 | 2 | 3 | 4 | 5;
}

function SimpleNavbarFunAndTrendy({
  colorTemplate = 1,
  ...props
}: SimpleNavbarFunAndTrendyProps) {
  return (
    <SiteThemeProvider
      theme={{
        styleVariant: "funAndTrendy",
        colorTemplate,
        textAnimation: "slide",
      }}
    >
      <SimpleNavbar {...props} />
    </SiteThemeProvider>
  );
}

SimpleNavbarFunAndTrendy.displayName = "SimpleNavbarFunAndTrendy";

export default memo(SimpleNavbarFunAndTrendy);
