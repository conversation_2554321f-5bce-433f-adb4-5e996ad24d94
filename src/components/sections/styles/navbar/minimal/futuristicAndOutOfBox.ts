import { futuristicTheme as theme } from "../../shared/themes";
import { ColorTemplate } from "../../shared/themeConfig";
import { getFuturisticColors } from "../../shared/themeConfig";
import { getButtonConfig } from "../../shared/styleHelpers";
import { MinimalNavbarStyle } from "../types";

export function getFuturisticMinimalNavbarStyle(
  colorTemplate: ColorTemplate = 1
): MinimalNavbarStyle {
  const colors = getFuturisticColors(colorTemplate);
  const buttonConfig = getButtonConfig('futuristic', 'primary', colorTemplate);

  return {
    navbar: {
      logoSrc: theme.navbar.logoSrc,
      logoWidth: 120,
      logoHeight: 40,
      logoClassName: `${theme.navbar.logoClassName}`,
      className: `fixed z-[100] flex items-center justify-between top-6 left-[var(--width-10)] right-[var(--width-10)] transition-all duration-500 ease-in-out`,
      containerClassName: "",
    },
    buttons: {
      buttonBgColor: buttonConfig.buttonBgColor,
      buttonHoverBgColor: buttonConfig.buttonHoverBgColor,
      buttonTextColor: buttonConfig.buttonTextColor,
      buttonHoverTextColor: buttonConfig.buttonHoverTextColor,
      buttonClassName: `px-6 py-2 rounded-lg border border-white/20 backdrop-blur-sm transition-all duration-300 hover:border-white/40`,
      buttonContentClassName: `text-sm font-medium ${theme.fonts.body.className}`,
    },
    button: {
      text: "Join Now",
      type: "stagger",
      buttonBgColor: buttonConfig.buttonBgColor,
      buttonHoverBgColor: buttonConfig.buttonHoverBgColor,
      buttonTextColor: buttonConfig.buttonTextColor,
      buttonHoverTextColor: buttonConfig.buttonHoverTextColor,
      buttonClassName: `relative px-6 h-10 z-100 rounded-lg border border-white/20 backdrop-blur-sm transition-all duration-300 hover:border-white/40`,
      buttonContentClassName: `rounded-full text-sm font-medium ${theme.fonts.body.className}`,
    },
  };
}

export const futuristicMinimalNavbarStyle = getFuturisticMinimalNavbarStyle(1);
