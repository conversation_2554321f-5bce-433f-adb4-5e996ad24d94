import { funAndTrendyTheme as theme } from "../../shared/themes";
import { ColorTemplate } from "../../shared/themeConfig";
import { getFunAndTrendyColors } from "../../shared/themeConfig";
import { getButtonConfig } from "../../shared/styleHelpers";
import { MinimalNavbarStyle } from "../types";

export function getFunAndTrendyMinimalNavbarStyle(
  colorTemplate: ColorTemplate = 1
): MinimalNavbarStyle {
  const colors = getFunAndTrendyColors(colorTemplate);
  const buttonConfig = getButtonConfig('funAndTrendy', 'primary', colorTemplate);

  return {
    navbar: {
      logoSrc: theme.navbar.logoSrc,
      logoWidth: 120,
      logoHeight: 40,
      logoClassName: `${theme.navbar.logoClassName}`,
      className: `fixed z-[100] flex items-center justify-between top-6 left-[var(--width-10)] right-[var(--width-10)] transition-all duration-500 ease-in-out`,
      containerClassName: "",
    },
    buttons: {
      buttonBgColor: buttonConfig.buttonBgColor,
      buttonHoverBgColor: buttonConfig.buttonHoverBgColor,
      buttonTextColor: buttonConfig.buttonTextColor,
      buttonHoverTextColor: buttonConfig.buttonHoverTextColor,
      buttonClassName: `${theme.navbar.buttonClassName}`,
      buttonContentClassName: `${theme.navbar.buttonContentClassName}`,
    },
    button: {
      text: "Join Now",
      type: "stagger",
      buttonBgColor: buttonConfig.buttonBgColor,
      buttonHoverBgColor: buttonConfig.buttonHoverBgColor,
      buttonTextColor: buttonConfig.buttonTextColor,
      buttonHoverTextColor: buttonConfig.buttonHoverTextColor,
      buttonClassName: `relative px-6 h-10 z-100 ${theme.navbar.buttonClassName}`,
      buttonContentClassName: `rounded-full ${theme.navbar.buttonContentClassName}`,
    },
  };
}

export const funAndTrendyMinimalNavbarStyle = getFunAndTrendyMinimalNavbarStyle(1);
