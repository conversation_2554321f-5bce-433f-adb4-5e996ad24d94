import { BaseSection, BaseTextConfig } from "../shared/types";

export interface NavbarStyle {
  navbar: {
    logoSrc: string;
    logoAlt?: string;
    logoWidth?: number;
    logoHeight?: number;
    logoClassName?: string;
    className?: string;
    containerClassName?: string;
  };
  buttons: {
    buttonBgColor?: string;
    buttonHoverBgColor?: string;
    buttonTextColor?: string;
    buttonHoverTextColor?: string;
    buttonClassName?: string;
    buttonContentClassName?: string;
  };
}

export interface SimpleNavbarStyle extends NavbarStyle {
  leftButton: {
    text?: string;
    buttonBgColor?: string;
    buttonHoverBgColor?: string;
    buttonTextColor?: string;
    buttonHoverTextColor?: string;
    buttonClassName?: string;
    buttonContentClassName?: string;
  };
  rightButton: {
    text?: string;
    buttonBgColor?: string;
    buttonHoverBgColor?: string;
    buttonTextColor?: string;
    buttonHoverTextColor?: string;
    buttonClassName?: string;
    buttonContentClassName?: string;
  };
}

export interface MinimalNavbarStyle extends NavbarStyle {
  button: {
    text?: string;
    type?: string;
    buttonBgColor?: string;
    buttonHoverBgColor?: string;
    buttonTextColor?: string;
    buttonHoverTextColor?: string;
    buttonClassName?: string;
    buttonContentClassName?: string;
  };
}
