import { futuristicTheme as theme } from "../../shared/themes";
import { ColorTemplate } from "../../shared/themeConfig";
import { getButtonConfig } from "../../shared/styleHelpers";
import { SimpleNavbarStyle } from "../types";

export function getFuturisticSimpleNavbarStyle(
  colorTemplate: ColorTemplate = 1
): SimpleNavbarStyle {
  const buttonConfig = getButtonConfig('futuristic', 'primary', colorTemplate);

  return {
    navbar: {
      logoSrc: theme.navbar.logoSrc,
      logoAlt: "Logo",
      logoClassName: `${theme.navbar.logoClassName} !h-8`,
      className: `fixed top-8 left-0 right-0 z-50`,
      containerClassName: `relative flex items-center justify-between px-[var(--width-10)]`,
    },
    buttons: {
      buttonBgColor: buttonConfig.buttonBgColor,
      buttonHoverBgColor: buttonConfig.buttonHoverBgColor,
      buttonTextColor: buttonConfig.buttonTextColor,
      buttonHoverTextColor: buttonConfig.buttonHoverTextColor,
      buttonClassName: `hidden md:flex px-6 py-2 rounded-lg border border-white/20 backdrop-blur-sm transition-all duration-300 hover:border-white/40`,
      buttonContentClassName: `text-sm font-medium ${theme.fonts.body.className}`,
    },
    leftButton: {
      text: "Menu",
      buttonBgColor: buttonConfig.buttonBgColor,
      buttonHoverBgColor: buttonConfig.buttonHoverBgColor,
      buttonTextColor: buttonConfig.buttonTextColor,
      buttonHoverTextColor: buttonConfig.buttonHoverTextColor,
      buttonClassName: `hidden md:flex px-6 py-2 rounded-lg border border-white/20 backdrop-blur-sm transition-all duration-300 hover:border-white/40`,
      buttonContentClassName: `text-sm font-medium ${theme.fonts.body.className}`,
    },
    rightButton: {
      text: "Contact",
      buttonBgColor: buttonConfig.buttonBgColor,
      buttonHoverBgColor: buttonConfig.buttonHoverBgColor,
      buttonTextColor: buttonConfig.buttonTextColor,
      buttonHoverTextColor: buttonConfig.buttonHoverTextColor,
      buttonClassName: `hidden md:flex px-6 py-2 rounded-lg border border-white/20 backdrop-blur-sm transition-all duration-300 hover:border-white/40`,
      buttonContentClassName: `text-sm font-medium ${theme.fonts.body.className}`,
    },
  };
}

export const futuristicSimpleNavbarStyle = getFuturisticSimpleNavbarStyle(1);
