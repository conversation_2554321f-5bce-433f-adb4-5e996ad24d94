import { funAndTrendyTheme as theme } from "../../shared/themes";
import { ColorTemplate } from "../../shared/themeConfig";
import { getFunAndTrendyColors } from "../../shared/themeConfig";
import { getButtonConfig } from "../../shared/styleHelpers";
import { SimpleNavbarStyle } from "../types";

export function getFunAndTrendySimpleNavbarStyle(
  colorTemplate: ColorTemplate = 1
): SimpleNavbarStyle {
  const colors = getFunAndTrendyColors(colorTemplate);
  const buttonConfig = getButtonConfig('funAndTrendy', 'primary', colorTemplate);

  return {
    navbar: {
      logoSrc: theme.navbar.logoSrc,
      logoAlt: "Logo",
      logoClassName: `${theme.navbar.logoClassName} !h-8`,
      className: `fixed top-8 left-0 right-0 z-50`,
      containerClassName: `relative flex items-center justify-between px-[var(--width-10)]`,
    },
    buttons: {
      buttonBgColor: buttonConfig.buttonBgColor,
      buttonHoverBgColor: buttonConfig.buttonHoverBgColor,
      buttonTextColor: buttonConfig.buttonTextColor,
      buttonHoverTextColor: buttonConfig.buttonHoverTextColor,
      buttonClassName: `hidden md:flex ${theme.navbar.buttonClassName}`,
      buttonContentClassName: `${theme.navbar.buttonContentClassName}`,
    },
    leftButton: {
      text: "Menu",
      buttonBgColor: buttonConfig.buttonBgColor,
      buttonHoverBgColor: buttonConfig.buttonHoverBgColor,
      buttonTextColor: buttonConfig.buttonTextColor,
      buttonHoverTextColor: buttonConfig.buttonHoverTextColor,
      buttonClassName: `hidden md:flex ${theme.navbar.buttonClassName}`,
      buttonContentClassName: `${theme.navbar.buttonContentClassName}`,
    },
    rightButton: {
      text: "Contact",
      buttonBgColor: buttonConfig.buttonBgColor,
      buttonHoverBgColor: buttonConfig.buttonHoverBgColor,
      buttonTextColor: buttonConfig.buttonTextColor,
      buttonHoverTextColor: buttonConfig.buttonHoverTextColor,
      buttonClassName: `hidden md:flex ${theme.navbar.buttonClassName}`,
      buttonContentClassName: `${theme.navbar.buttonContentClassName}`,
    },
  };
}

export const funAndTrendySimpleNavbarStyle = getFunAndTrendySimpleNavbarStyle(1);
