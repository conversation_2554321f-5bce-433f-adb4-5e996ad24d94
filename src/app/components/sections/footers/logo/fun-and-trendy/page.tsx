"use client";

import React, { Suspense } from "react";
import { React<PERSON>enis } from "lenis/react";
import { PageNav } from "@/components/common/PageNav";
import { useSearchParams } from "next/navigation";
import { SiteThemeProvider, ColorTemplate } from "@/components/sections/ThemeProvider";
import LogoFooter from "@/components/sections/layouts/footer/LogoFooter";

function FunAndTrendyLogoFooterContent() {
  const searchParams = useSearchParams();
  const colorTemplate = (Number(searchParams.get("theme")) || 1) as ColorTemplate;

  const footerData = {
    columns: [
      {
        items: [
          { label: "How it Works", onClick: () => console.log("How it Works clicked") },
          { label: "What's Included", onClick: () => console.log("What's Included clicked") },
          { label: "Gift Superpower", onClick: () => console.log("Gift Superpower clicked") },
          { label: "Member Benefits", onClick: () => console.log("Member Benefits clicked") },
        ],
      },
      {
        items: [
          { label: "Member Login", onClick: () => console.log("Member Login clicked") },
          { label: "Manifesto", onClick: () => console.log("Manifesto clicked") },
          { label: "Join the Team", onClick: () => console.log("Join the Team clicked") },
          { label: "Superpower Labs", onClick: () => console.log("Superpower Labs clicked") },
        ],
      },
      {
        items: [
          { label: "For Creators", onClick: () => console.log("For Creators clicked") },
          { label: "For Partners", onClick: () => console.log("For Partners clicked") },
          { label: "For Teams", onClick: () => console.log("For Teams clicked") },
          { label: "For Enterprise", onClick: () => console.log("For Enterprise clicked") },
        ],
      },
      {
        items: [
          { label: "X/Twitter", onClick: () => console.log("X/Twitter clicked") },
          { label: "Instagram", onClick: () => console.log("Instagram clicked") },
          { label: "LinkedIn", onClick: () => console.log("LinkedIn clicked") },
          { label: "YouTube", onClick: () => console.log("YouTube clicked") },
        ],
      },
      {
        items: [
          { label: "Terms", onClick: () => console.log("Terms clicked") },
          { label: "Privacy Policy", onClick: () => console.log("Privacy Policy clicked") },
          { label: "FAQ", onClick: () => console.log("FAQ clicked") },
          { label: "Contact us", onClick: () => console.log("Contact us clicked") },
        ],
      },
    ],
  };

  return (
    <SiteThemeProvider theme={{ styleVariant: 'funAndTrendy', colorTemplate, textAnimation: 'slide' }}>
      <LogoFooter {...footerData} />
    </SiteThemeProvider>
  );
}

export default function FunAndTrendyLogoFooterPage() {
  return (
    <ReactLenis root>
      <PageNav position="bottom" />
      <Suspense fallback={<div>Loading...</div>}>
        <FunAndTrendyLogoFooterContent />
      </Suspense>
    </ReactLenis>
  );
}