"use client";

import React, { Suspense } from "react";
import { ReactLenis } from "lenis/react";
import { PageNav } from "@/components/common/PageNav";
import { useSearchParams } from "next/navigation";
import { SiteThemeProvider, ColorTemplate } from '@/components/sections/ThemeProvider';
import MewFooter from "@/components/sections/layouts/footer/MewFooter";

function FuturisticMewFooterContent() {
  const searchParams = useSearchParams();
  const colorTemplate = (Number(searchParams.get("theme")) || 1) as ColorTemplate;

  const footerData = {
    title: "Let's come together and put an end to their tyranny… The dog days are done.",
    socialLinks: [
      { platform: "github", onClick: () => console.log("Github clicked") },
      { platform: "instagram", onClick: () => console.log("Instagram clicked") },
      { platform: "framer", onClick: () => console.log("Framer clicked") },
      { platform: "twitter", onClick: () => console.log("Twitter clicked") },
    ],
  };

  return (
    <SiteThemeProvider theme={{ styleVariant: 'futuristicAndOutOfBox', colorTemplate: colorTemplate, textAnimation: 'none' }}>
      <MewFooter
      {...footerData}
     />
    </SiteThemeProvider>
  );
}

export default function FuturisticMewFooterPage() {
  return (
    <ReactLenis root>
      <PageNav position="bottom" />
      <Suspense fallback={<div>Loading...</div>}>
        <FuturisticMewFooterContent />
      </Suspense>
    </ReactLenis>
  );
}
