"use client";

import React, { Suspense } from "react";
import { ReactLenis } from "lenis/react";
import { PageNav } from "@/components/common/PageNav";
import { useSearchParams } from "next/navigation";
import { SiteThemeProvider, ColorTemplate } from "@/components/sections/ThemeProvider";
import GradientFooter from "@/components/sections/layouts/footer/GradientFooter";

function FunAndTrendyGradientFooterContent() {
  const searchParams = useSearchParams();
  const theme = (Number(searchParams.get("theme")) || 1) as ColorTemplate;


  return (
    <SiteThemeProvider theme={{ styleVariant: 'funAndTrendy', colorTemplate: theme, textAnimation: 'slide' }}>
      <GradientFooter />
    </SiteThemeProvider>
  );
}

export default function FunAndTrendyGradientFooterPage() {
  return (
    <ReactLenis root>
      <PageNav position="bottom" />
      <Suspense fallback={<div>Loading...</div>}>
        <FunAndTrendyGradientFooterContent />
      </Suspense>
    </ReactLenis>
  );
}