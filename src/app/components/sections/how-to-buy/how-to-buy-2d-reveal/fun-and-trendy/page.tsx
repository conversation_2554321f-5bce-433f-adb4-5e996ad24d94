"use client";

import React, { Suspense } from "react";
import { ReactLenis } from "lenis/react";
import HowToBuy2D from "@/components/sections/layouts/howtobuy/HowToBuy2D";
import { PageNav } from "@/components/common/PageNav";
import { useSearchParams } from "next/navigation";
import { SiteThemeProvider, ColorTemplate } from '@/components/sections/ThemeProvider';

function FunAndTrendyContent() {
  const searchParams = useSearchParams();
  const colorTemplate = (Number(searchParams.get("theme")) || 1) as ColorTemplate;

  return (
    <ReactLenis root>
      <PageNav position="bottom" />
      <SiteThemeProvider theme={{ styleVariant: 'funAndTrendy', colorTemplate: colorTemplate, textAnimation: 'slide' }}>
        <HowToBuy2D />
      </SiteThemeProvider>
    </ReactLenis>
  );
}

export default function FunAndTrendyHowToBuy2DPage() {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <FunAndTrendyContent />
    </Suspense>
  );
}
