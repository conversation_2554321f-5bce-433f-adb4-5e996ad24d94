"use client";

import React, { Suspense } from "react";
import { ReactLenis } from "lenis/react";
import HowToBuy2D from "@/components/sections/layouts/howtobuy/HowToBuy2D";
import { PageNav } from "@/components/common/PageNav";
import { useSearchParams } from "next/navigation";
import { SiteThemeProvider, ColorTemplate } from '@/components/sections/ThemeProvider';

function FuturisticMinimalContent() {
  const searchParams = useSearchParams();
  const colorTemplate = (Number(searchParams.get("theme")) || 1) as ColorTemplate;

  return (
    <ReactLenis root>
      <PageNav position="bottom" />
      <SiteThemeProvider theme={{ styleVariant: 'futuristicAndOutOfBox', colorTemplate: colorTemplate, textAnimation: 'none' }}>
        <HowToBuy2D />
      </SiteThemeProvider>
    </ReactLenis>
  );
}

export default function FuturisticMinimalHowToBuy2DPage() {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <FuturisticMinimalContent />
    </Suspense>
  );
}
