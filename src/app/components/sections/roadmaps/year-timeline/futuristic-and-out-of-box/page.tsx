"use client";

import React, { Suspense } from "react";
import { <PERSON><PERSON><PERSON>eni<PERSON> } from "lenis/react";
import YearRoadmapTimeline from "@/components/sections/layouts/roadmap/YearRoadmapTimeline";
import { PageNav } from "@/components/common/PageNav";
import { useSearchParams } from "next/navigation";
import { SiteThemeProvider, ColorTemplate } from '@/components/sections/ThemeProvider';
import { type YearTimelineItem } from "@/components/timeline/YearTimeline";

function FuturisticYearTimelineContent() {
  const searchParams = useSearchParams();
  const colorTemplate = (Number(searchParams.get("theme")) || 1) as ColorTemplate;

  const roadmapData: YearTimelineItem[] = [
    {
      year: "2024",
      title: "Project Genesis",
      description:
        "Foundation phase with comprehensive market research, team assembly, initial funding rounds, technology stack selection, and establishment of core development infrastructure to ensure solid project groundwork",
    },
    {
      year: "2025",
      title: "Alpha Development",
      description:
        "Core feature development including MVP creation, initial user interface design, backend architecture implementation, security protocols establishment, and alpha testing with select user groups",
    },
    {
      year: "2026",
      title: "Beta Launch & Expansion",
      description:
        "Public beta release with community feedback integration, feature refinement, scalability improvements, partnership development, and preparation for full market launch with enhanced capabilities",
    },
    {
      year: "2027",
      title: "Global Deployment",
      description:
        "Full production launch with worldwide availability, advanced feature rollouts, enterprise solutions, comprehensive support systems, and continuous innovation to maintain market leadership position",
    },
    {
      year: "2028",
      title: "Ecosystem Maturity",
      description:
        "Complete ecosystem development with third-party integrations, advanced analytics, AI-powered features, global expansion completion, and establishment as industry standard platform",
    },
  ];

  return (
    <ReactLenis root>
      <div className="min-h-screen">
        <PageNav position="bottom" />
        <SiteThemeProvider theme={{ styleVariant: 'futuristicAndOutOfBox', colorTemplate: colorTemplate, textAnimation: 'none' }}>
      <YearRoadmapTimeline
          items={roadmapData} 
         />
    </SiteThemeProvider>
      </div>
    </ReactLenis>
  );
}

export default function FuturisticYearTimelinePage() {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <FuturisticYearTimelineContent />
    </Suspense>
  );
}