"use client";

import React, { Suspense } from "react";
import { <PERSON><PERSON><PERSON>eni<PERSON> } from "lenis/react";
import StackRoadmapTimeline from "@/components/sections/layouts/roadmap/StackRoadmapTimeline";
import { PageNav } from "@/components/common/PageNav";
import { useSearchParams } from "next/navigation";
import { SiteThemeProvider, ColorTemplate } from '@/components/sections/ThemeProvider';
import type { StackTimelineItem } from "@/types/timeline";

function FunAndTrendyStackTimelineRoadmapContent() {
  const searchParams = useSearchParams();
  const colorTemplate = (Number(searchParams.get("theme")) || 1) as ColorTemplate;

  const roadmapStackItems: StackTimelineItem[] = [
    {
      id: 1,
      title: "Strategic Foundation & Planning",
      description:
        "Comprehensive market research, competitive analysis, team assembly, and strategic roadmap development. This phase establishes the core foundation for project success with detailed planning and resource allocation.",
      image: "/images/placeholder1.avif",
    },
    {
      id: 2,
      title: "Design & User Experience",
      description:
        "User-centered design approach including wireframing, prototyping, visual identity creation, and usability testing. Focus on creating intuitive and engaging user experiences across all touchpoints.",
      image: "/images/placeholder2.avif",
    },
    {
      id: 3,
      title: "MVP Development & Testing",
      description:
        "Agile development of minimum viable product with core features, continuous integration, quality assurance testing, and iterative improvements based on user feedback and performance metrics.",
      image: "/images/placeholder3.avif",
    },
    {
      id: 4,
      title: "Launch & Market Entry",
      description:
        "Strategic product launch with comprehensive marketing campaigns, user onboarding systems, performance monitoring, and initial market penetration strategies for sustainable growth.",
      image: "/images/placeholder4.avif",
    },
    {
      id: 5,
      title: "Scale & Optimization",
      description:
        "Platform scaling, feature expansion, performance optimization, advanced analytics implementation, and strategic partnerships to establish market leadership and drive long-term success.",
      image: "/images/placeholder1.avif",
    },
  ];

  return (
    <ReactLenis root>
      <div className="min-h-screen">
        <PageNav />
        <div className="h-screen flex items-center justify-center">
          <div className="text-center">
            <h1 className="text-4xl font-bold mb-4">Stack Timeline Roadmap</h1>
            <h2 className="text-2xl mb-4">Fun & Trendy Style</h2>
            <p className="text-base mb-4">Theme: {colorTemplate}</p>
            <p className="text-base">Scroll down to see the stack roadmap</p>
          </div>
        </div>
        <SiteThemeProvider theme={{ styleVariant: 'funAndTrendy', colorTemplate: colorTemplate, textAnimation: 'slide' }}>
      <StackRoadmapTimeline
          items={roadmapStackItems} 
         />
    </SiteThemeProvider>
        <div className="h-screen flex items-center justify-center">
          <p className="text-base text-center">End of roadmap</p>
        </div>
      </div>
    </ReactLenis>
  );
}

export default function FunAndTrendyStackTimelineRoadmapPage() {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <FunAndTrendyStackTimelineRoadmapContent />
    </Suspense>
  );
}
