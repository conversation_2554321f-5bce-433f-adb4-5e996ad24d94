'use client';

import React, { Suspense } from 'react';
import { ReactLenis } from 'lenis/react';
import CyclopsHero from '@/components/sections/layouts/hero/CyclopsHero';
import { PageNav } from '@/components/common/PageNav';
import { useSearchParams } from 'next/navigation';
import { SiteThemeProvider, ColorTemplate } from '@/components/sections/ThemeProvider';

function FuturisticCyclopsHeroContent() {
  const searchParams = useSearchParams();
  const colorTemplate = (searchParams.get('theme') as unknown as ColorTemplate) || 1;
  
  const handleBuyClick = () => {
    console.log('Buy button clicked');
    // Add your buy logic here
  };

  const handleJoinCultClick = () => {
    console.log('Join the Cult button clicked');
    // Add your join cult logic here
  };

  return (
    <ReactLenis root>
      <PageNav position="bottom" />
      <SiteThemeProvider theme={{ styleVariant: 'futuristicAndOutOfBox', colorTemplate: Number(colorTemplate) as ColorTemplate, textAnimation: 'none' }}>
      <CyclopsHero
        onPrimaryButtonClick={handleBuyClick}
        onSecondaryButtonClick={handleJoinCultClick}
       />
    </SiteThemeProvider>
    </ReactLenis>
  );
}

export default function FuturisticCyclopsHeroPage() {
  return (
    <Suspense>
      <FuturisticCyclopsHeroContent />
    </Suspense>
  );
}
