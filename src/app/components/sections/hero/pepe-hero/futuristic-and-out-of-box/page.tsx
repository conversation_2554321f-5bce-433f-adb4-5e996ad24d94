"use client";

import React, { Suspense } from "react";
import { React<PERSON>enis } from "lenis/react";
import PepeHero from "@/components/sections/layouts/hero/PepeHero";
import { PageNav } from "@/components/common/PageNav";
import { useSearchParams } from "next/navigation";
import { SiteThemeProvider, ColorTemplate } from '@/components/sections/ThemeProvider';

function FuturisticPepeHeroContent() {
  const searchParams = useSearchParams();
  const colorTemplate = (Number(searchParams.get("theme")) || 1) as ColorTemplate;

  return (
    <ReactLenis root>
      <PageNav position="bottom" />
      <SiteThemeProvider theme={{ styleVariant: 'futuristicAndOutOfBox', colorTemplate: colorTemplate, textAnimation: 'none' }}>
        <PepeHero />
      </SiteThemeProvider>
    </ReactLenis>
  );
}

export default function FuturisticPepePage() {
  return (
    <Suspense>
      <FuturisticPepeHeroContent />
    </Suspense>
  );
}
