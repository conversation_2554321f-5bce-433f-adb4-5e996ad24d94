"use client";

import React from "react";
import Link from "next/link";
import { PageNav } from "@/components/common/PageNav";

const heroTypes: Array<{ name: string; href: string }> = [
  {
    name: 'Playful Hero',
    href: '/components/sections/hero/playful-hero'
  },
  {
    name: 'Techy Hero',
    href: '/components/sections/hero/techy-hero'
  },
  {
    name: 'Pepe Hero',
    href: '/components/sections/hero/pepe-hero'
  },
  {
    name: 'Cyclops Hero',
    href: '/components/sections/hero/cyclops-hero'
  },
  {
    name: 'Simple Hero',
    href: '/components/sections/hero/simple-hero'
  }
];

export default function HeroPage() {
  return (
    <section className="min-h-screen py-[var(--width-10)]">
      <PageNav />
      <div className="w-full px-[var(--width-10)]">
        <div className="grid grid-cols-2 lg:grid-cols-4 gap-4">
          {heroTypes.map((item) => (
            <Link key={item.name} href={item.href}>
              <div className="aspect-square card relative rounded p-6 flex justify-center items-center text-center cursor-pointer">
                <h3 className="text-xl">{item.name}</h3>
              </div>
            </Link>
          ))}
        </div>
      </div>
    </section>
  );
}