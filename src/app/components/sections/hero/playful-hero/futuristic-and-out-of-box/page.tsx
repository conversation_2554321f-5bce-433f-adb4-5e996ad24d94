'use client'

import React, { Suspense } from 'react'
import { ReactLenis } from 'lenis/react'
import PlayfulHero from '@/components/sections/layouts/hero/PlayfulHero'
import { PageNav } from '@/components/common/PageNav'
import { useSearchParams } from 'next/navigation'
import { SiteThemeProvider, ColorTemplate } from '@/components/sections/ThemeProvider';

function FuturisticAndOutOfBoxContent() {
  const searchParams = useSearchParams()
  const colorTemplate = (Number(searchParams.get('theme')) || 1) as ColorTemplate
  
  return (
    <SiteThemeProvider theme={{ styleVariant: 'futuristicAndOutOfBox', colorTemplate: colorTemplate, textAnimation: 'none' }}>
        <PlayfulHero />
      </SiteThemeProvider>
  )
}

export default function FuturisticAndOutOfBoxPage() {
  return (
    <ReactLenis root>
      <PageNav position="bottom" />
      <Suspense>
        <FuturisticAndOutOfBoxContent />
      </Suspense>
    </ReactLenis>
  )
}