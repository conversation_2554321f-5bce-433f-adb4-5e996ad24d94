'use client'

import React from 'react'
import { ReactLenis } from 'lenis/react'
import { PageNav } from '@/components/common/PageNav'
import { useSearchParams } from 'next/navigation'
import { Suspense } from 'react'
import PatternTokenomics from '@/components/sections/layouts/tokenomics/PatternTokenomics'
import { SiteThemeProvider, ColorTemplate } from '@/components/sections/ThemeProvider';

function FunAndTrendyTokenomics() {
  const searchParams = useSearchParams()
  const colorTemplate = (Number(searchParams.get('theme')) || 1) as ColorTemplate

  return (
    <ReactLenis root>
      <PageNav position="bottom" />
      <SiteThemeProvider theme={{ styleVariant: 'funAndTrendy', colorTemplate: colorTemplate, textAnimation: 'slide' }}>
        <PatternTokenomics />
      </SiteThemeProvider>
    </ReactLenis>
  )
}

export default function FunAndTrendyTokenomicsPage() {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <FunAndTrendyTokenomics />
    </Suspense>
  )
}
