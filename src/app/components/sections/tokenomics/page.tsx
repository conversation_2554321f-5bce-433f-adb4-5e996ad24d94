"use client";

import React from "react";
import Link from "next/link";
import { PageNav } from "@/components/common/PageNav";

const tokenomicsItems: Array<{ name: string; href: string }> = [
  {
    name: "Pudgy Tokenomics",
    href: "/components/sections/tokenomics/pudgy",
  },
  {
    name: "Minimal Tokenomics",
    href: "/components/sections/tokenomics/minimal",
  },
  {
    name: "Simple Tokenomics",
    href: "/components/sections/tokenomics/simple",
  },
  {
    name: "Expanding Tokenomics",
    href: "/components/sections/tokenomics/expanding",
  },
  {
    name: "Pattern Tokenomics",
    href: "/components/sections/tokenomics/pattern",
  }
];

export default function TokenomicsPage() {
  return (
    <section className="min-h-screen py-[var(--width-10)]">
      <PageNav />
      <div className="w-full px-[var(--width-10)]">
        {tokenomicsItems.length === 0 ? (
          <div className="flex items-center justify-center h-screen">
            <p className="text-lg text-gray-500">No items available yet</p>
          </div>
        ) : (
          <div className="grid grid-cols-2 lg:grid-cols-4 gap-4">
            {tokenomicsItems.map((item) => (
              <Link key={item.name} href={item.href}>
                <div className="aspect-square card relative rounded p-6 flex justify-center items-center text-center cursor-pointer">
                  <h3 className="text-xl">{item.name}</h3>
                </div>
              </Link>
            ))}
          </div>
        )}
      </div>
    </section>
  );
}
