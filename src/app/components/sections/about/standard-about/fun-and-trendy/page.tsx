'use client'

import React from 'react'
import { ReactLenis } from 'lenis/react'
import StandardAbout from '@/components/sections/layouts/about/StandardAbout'
import { PageNav } from '@/components/common/PageNav'
import { SiteThemeProvider } from '@/components/sections/ThemeProvider'

export default function FunAndTrendyAboutPage() {
  return (
    <ReactLenis root>
      <PageNav position="bottom" />
      <SiteThemeProvider theme={{ styleVariant: 'funAndTrendy', colorTemplate: 1, textAnimation: 'slide' }}>
        <StandardAbout />
      </SiteThemeProvider>
    </ReactLenis>
  )
}