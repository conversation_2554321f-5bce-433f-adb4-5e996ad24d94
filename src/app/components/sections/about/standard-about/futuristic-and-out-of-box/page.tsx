'use client'

import React from 'react'
import { ReactLenis } from 'lenis/react'
import StandardAbout from '@/components/sections/layouts/about/StandardAbout'
import { PageNav } from '@/components/common/PageNav'
import { SiteThemeProvider, ColorTemplate } from '@/components/sections/ThemeProvider'

export default function FuturisticAndOutOfBoxAboutPage() {
  return (
    <ReactLenis root>
      <PageNav position="bottom" />
      <SiteThemeProvider theme={{ styleVariant: 'futuristicAndOutOfBox', colorTemplate: 1 as ColorTemplate, textAnimation: 'slide' }}>
        <StandardAbout />
      </SiteThemeProvider>
    </ReactLenis>
  )
}