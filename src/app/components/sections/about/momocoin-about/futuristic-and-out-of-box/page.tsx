"use client";

import React, { Suspense } from "react";
import { ReactLenis } from "lenis/react";
import { PageNav } from "@/components/common/PageNav";
import MomocoinAbout from "@/components/sections/layouts/about/MomocoinAbout";
import { useSearchParams } from "next/navigation";
import { SiteThemeProvider, ColorTemplate } from "@/components/sections/ThemeProvider";

function FuturisticAboutPage() {
  const searchParams = useSearchParams();
  const colorTemplate = (searchParams.get("theme") as unknown as ColorTemplate) || 1;

  return (
    <ReactLenis root>
      <PageNav position="bottom" />
      <SiteThemeProvider theme={{ styleVariant: 'futuristicAndOutOfBox', colorTemplate: Number(colorTemplate) as ColorTemplate, textAnimation: 'none' }}>
        <MomocoinAbout 
          descriptions={[
            "<PERSON><PERSON> is <PERSON><PERSON>'s cheeky little sister - pastel pink energy ready to pounce born on Solana. Built for fun. By the people. For the people. 100% fair launched, no cabal, no bundles."
          ]}
        />
      </SiteThemeProvider>
    </ReactLenis>
  );
}

export default function FunAndTrendyAboutPage() {
  return (
    <Suspense>
      <FuturisticAboutPage />
    </Suspense>
  );
}
