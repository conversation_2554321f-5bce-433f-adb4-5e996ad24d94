"use client";

import React, { Suspense } from "react";
import { ReactLenis } from "lenis/react";
import { PageNav } from "@/components/common/PageNav";
import MomocoinAbout from "@/components/sections/layouts/about/MomocoinAbout";
import { useSearchParams } from "next/navigation";
import { SiteThemeProvider, ColorTemplate } from "@/components/sections/ThemeProvider";

function FunAndTrendyAboutContent() {
  const searchParams = useSearchParams();
  const colorTemplate = (searchParams.get("theme") as unknown as ColorTemplate) || 1;

  return (
    <ReactLenis root>
      <PageNav position="bottom" />
      <SiteThemeProvider theme={{ styleVariant: 'funAndTrendy', colorTemplate: Number(colorTemplate) as ColorTemplate, textAnimation: 'slide' }}>
        <MomocoinAbout />
      </SiteThemeProvider>
    </ReactLenis>
  );
}

export default function FunAndTrendyAboutPage() {
  return (
    <Suspense>
      <FunAndTrendyAboutContent />
    </Suspense>
  );
}
