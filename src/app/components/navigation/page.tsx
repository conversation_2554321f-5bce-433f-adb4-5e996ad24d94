"use client";

import React from "react";
import Link from "next/link";
import { PageNav } from "@/components/common/PageNav";

const navigations: Array<{ name: string; href: string }> = [
  {
    name: "Apple Style Navbar",
    href: "/components/navigation/apple-style-navbar",
  },
  {
    name: "Floating Navbar",
    href: "/components/navigation/floating-navbar",
  },
  {
    name: "Simple Floating Navbar",
    href: "/components/navigation/simple-floating-navbar",
  },
  {
    name: "Minimal Navbar",
    href: "/components/navigation/minimal-navbar",
  },
  {
    name: "Split Navbar",
    href: "/components/navigation/split-navbar",
  },
  {
    name: "Simple Navbar",
    href: "/components/navigation/simple-navbar",
  },
  {
    name: "Simple Navbar Variations",
    href: "/components/navigation/simple-navbar-variations",
  },
  {
    name: "Minimal Navbar Variations",
    href: "/components/navigation/minimal-navbar-variations",
  },
];

export default function NavigationPage() {
  return (
    <section className="min-h-screen py-[var(--width-10)]">
      <PageNav />
      <div className="w-full px-[var(--width-10)]">
        <div className="grid grid-cols-2 lg:grid-cols-4 gap-4">
          {navigations.map((navigation) => (
            <Link key={navigation.name} href={navigation.href}>
              <div className="aspect-square card relative rounded p-6 flex justify-center items-center text-center cursor-pointer">
                <h3 className="text-xl">{navigation.name}</h3>
              </div>
            </Link>
          ))}
        </div>
      </div>
    </section>
  );
}
