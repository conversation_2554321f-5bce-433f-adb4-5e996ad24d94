"use client";

import { PageNav } from "@/components/common/PageNav";
import { ReactLenis } from "lenis/react";
import { NavItem } from "@/types/navigation";
import SimpleFloatingNavbar from "@/components/navigation/SimpleFloatingNavbar";

export default function SimpleFloatingNavbarPage() {
  const navItems: NavItem[] = [
    { name: "Home", id: "home" },
    { name: "About", id: "about" },
    { name: "Solutions", id: "solutions" },
    { name: "Community", id: "community" },
  ];

  return (
    <ReactLenis root>
      <PageNav position="bottom" />
      <SimpleFloatingNavbar
        navItems={navItems}
        logoSrc="/images/logo.svg"
        logoWidth={120}
        logoHeight={40}
        buttonText="Get Started"
        onButtonClick={() => console.log("Get Started clicked")}
      />
      <section
        id="home"
        className="h-screen flex items-center justify-center text-black"
      >
        <p>Home</p>
      </section>
      <section
        id="about"
        className="h-screen flex items-center justify-center text-black"
      >
        <p>About</p>
      </section>
      <section
        id="solutions"
        className="h-screen flex items-center justify-center text-black"
      >
        <p>Solutions</p>
      </section>
      <section
        id="community"
        className="h-screen flex items-center justify-center text-black"
      >
        <p>Community</p>
      </section>
    </ReactLenis>
  );
}
