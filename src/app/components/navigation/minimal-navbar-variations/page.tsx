"use client";

import { React<PERSON>enis } from "lenis/react";
import {
  MinimalNavbarFunAndTrendy,
  MinimalNavbarFuturistic,
} from "@/components/sections/layouts/navbars";
import { PageNav } from "@/components/common/PageNav";

export default function MinimalNavbarVariationsPage() {
  return (
    <ReactLenis root>
      <PageNav position="bottom" />
      
      {/* Fun and Trendy Variation */}
      <section className="h-screen bg-gradient-to-br from-orange-500 to-red-600 relative">
        <MinimalNavbarFunAndTrendy
          buttonText="Get Started"
          onButtonClick={() => console.log("Fun & Trendy Get Started clicked")}
        />
        <div className="h-full flex items-center justify-center">
          <div className="text-center text-white">
            <h1 className="text-6xl font-bold mb-4">Fun & Trendy</h1>
            <p className="text-xl">MinimalNavbar with retro styling</p>
          </div>
        </div>
      </section>

      {/* Futuristic Variation */}
      <section className="h-screen bg-gradient-to-br from-indigo-900 to-purple-900 relative">
        <MinimalNavbarFuturistic
          buttonText="Get Started"
          onButtonClick={() => console.log("Futuristic Get Started clicked")}
        />
        <div className="h-full flex items-center justify-center">
          <div className="text-center text-white">
            <h1 className="text-6xl font-bold mb-4">Futuristic</h1>
            <p className="text-xl">MinimalNavbar with modern styling</p>
          </div>
        </div>
      </section>

      {/* Slide Button Variation */}
      <section className="h-screen bg-gradient-to-br from-cyan-600 to-blue-600 relative">
        <MinimalNavbarFunAndTrendy
          buttonText="Join Now"
          buttonType="slide"
          onButtonClick={() => console.log("Slide button clicked")}
        />
        <div className="h-full flex items-center justify-center">
          <div className="text-center text-white">
            <h1 className="text-6xl font-bold mb-4">Slide Button</h1>
            <p className="text-xl">Fun & Trendy with slide button</p>
          </div>
        </div>
      </section>

      {/* Color Template Variations */}
      <section className="h-screen bg-gradient-to-br from-emerald-600 to-green-600 relative">
        <MinimalNavbarFuturistic
          colorTemplate={3}
          buttonText="Explore"
          onButtonClick={() => console.log("Color Template 3 button clicked")}
        />
        <div className="h-full flex items-center justify-center">
          <div className="text-center text-white">
            <h1 className="text-6xl font-bold mb-4">Color Template 3</h1>
            <p className="text-xl">Futuristic with different colors</p>
          </div>
        </div>
      </section>
    </ReactLenis>
  );
}
