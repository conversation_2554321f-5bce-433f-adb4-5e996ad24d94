"use client";

import React from "react";
import { React<PERSON>enis } from "lenis/react";
import SimpleNavbar from "@/components/sections/layouts/navbars/SimpleNavbar";
import { PageNav } from "@/components/common/PageNav";

export default function SimpleNavbarPage() {
  return (
    <ReactLenis root>
      <PageNav position="bottom" />
      <SimpleNavbar
        logoSrc="/images/logo.svg"
        leftButtonText="Menu"
        rightButtonText="Contact"
        onLeftButtonClick={() => console.log("Left button clicked")}
        onRightButtonClick={() => console.log("Right button clicked")}
      />
      <div className="h-screen flex items-center justify-center">
        <p className="text-base text-center">Section</p>
      </div>
      <div className="h-screen flex items-center justify-center">
        <p className="text-base text-center">Section</p>
      </div>
      <div className="h-screen flex items-center justify-center">
        <p className="text-base text-center">Section</p>
      </div>
    </ReactLenis>
  );
}
