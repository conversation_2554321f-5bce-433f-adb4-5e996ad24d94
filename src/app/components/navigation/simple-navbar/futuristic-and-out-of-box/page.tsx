"use client";

import React, { Suspense } from "react";
import { useSearchParams } from "next/navigation";
import { ReactLenis } from "lenis/react";
import SimpleNavbar from "@/components/sections/layouts/navbars/SimpleNavbar";
import { PageNav } from "@/components/common/PageNav";
import { SiteThemeProvider, ColorTemplate } from '@/components/sections/ThemeProvider';

function FuturisticNavbarContent() {
  const searchParams = useSearchParams();
  const colorTemplate = (searchParams.get("theme") as unknown as ColorTemplate) || 1;

  return (
    <ReactLenis root>
      <PageNav position="bottom" />
      <SiteThemeProvider theme={{ styleVariant: 'futuristicAndOutOfBox', colorTemplate: Number(colorTemplate) as ColorTemplate, textAnimation: 'slide' }}>
        <SimpleNavbar
          leftButtonText="Menu"
          rightButtonText="Contact"
          onLeftButtonClick={() => console.log("Left button clicked")}
          onRightButtonClick={() => console.log("Right button clicked")}
        />
        <section className="h-screen bg-gradient-to-br from-slate-900 to-blue-900 flex items-center justify-center">
          <div className="text-center text-white">
            <h1 className="text-6xl font-bold mb-4">Futuristic</h1>
            <p className="text-xl">SimpleNavbar with modern styling</p>
            <p className="text-lg mt-2">Color Template: {colorTemplate}</p>
          </div>
        </section>
        <section className="h-screen bg-gradient-to-br from-indigo-900 to-purple-900 flex items-center justify-center">
          <div className="text-center text-white">
            <h2 className="text-4xl font-bold mb-4">Section 2</h2>
            <p className="text-lg">Scroll to see navbar behavior</p>
          </div>
        </section>
        <section className="h-screen bg-gradient-to-br from-cyan-900 to-blue-900 flex items-center justify-center">
          <div className="text-center text-white">
            <h2 className="text-4xl font-bold mb-4">Section 3</h2>
            <p className="text-lg">More content to demonstrate scrolling</p>
          </div>
        </section>
      </SiteThemeProvider>
    </ReactLenis>
  );
}

export default function FuturisticPage() {
  return (
    <Suspense>
      <FuturisticNavbarContent />
    </Suspense>
  );
}
