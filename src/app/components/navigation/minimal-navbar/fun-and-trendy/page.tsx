"use client";

import React, { Suspense } from "react";
import { useSearchParams } from "next/navigation";
import { ReactLenis } from "lenis/react";
import MinimalNavbar from "@/components/sections/layouts/navbars/MinimalNavbar";
import { PageNav } from "@/components/common/PageNav";
import { SiteThemeProvider, ColorTemplate } from '@/components/sections/ThemeProvider';

function FunAndTrendyNavbarContent() {
  const searchParams = useSearchParams();
  const colorTemplate = (searchParams.get("theme") as unknown as ColorTemplate) || 1;

  return (
    <ReactLenis root>
      <PageNav position="bottom" />
      <SiteThemeProvider theme={{ styleVariant: 'funAndTrendy', colorTemplate: Number(colorTemplate) as ColorTemplate, textAnimation: 'slide' }}>
        <MinimalNavbar
          buttonText="Get Started"
          onButtonClick={() => console.log("Get Started clicked")}
        />
        <section className="h-screen bg-gradient-to-br from-orange-500 to-red-600 flex items-center justify-center">
          <div className="text-center text-white">
            <h1 className="text-6xl font-bold mb-4">Fun & Trendy</h1>
            <p className="text-xl">MinimalNavbar with retro styling</p>
            <p className="text-lg mt-2">Color Template: {colorTemplate}</p>
          </div>
        </section>
        <section className="h-screen bg-gradient-to-br from-purple-600 to-pink-500 flex items-center justify-center">
          <div className="text-center text-white">
            <h2 className="text-4xl font-bold mb-4">Section 2</h2>
            <p className="text-lg">Scroll to see navbar behavior</p>
          </div>
        </section>
        <section className="h-screen bg-gradient-to-br from-green-600 to-teal-500 flex items-center justify-center">
          <div className="text-center text-white">
            <h2 className="text-4xl font-bold mb-4">Section 3</h2>
            <p className="text-lg">More content to demonstrate scrolling</p>
          </div>
        </section>
      </SiteThemeProvider>
    </ReactLenis>
  );
}

export default function FunAndTrendyPage() {
  return (
    <Suspense>
      <FunAndTrendyNavbarContent />
    </Suspense>
  );
}
