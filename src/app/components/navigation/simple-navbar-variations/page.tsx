"use client";

import { React<PERSON>enis } from "lenis/react";
import {
  SimpleNavbarFunAndTrendy,
  SimpleNavbarFuturistic,
} from "@/components/sections/layouts/navbars";
import { PageNav } from "@/components/common/PageNav";

export default function SimpleNavbarVariationsPage() {
  return (
    <ReactLenis root>
      <PageNav position="bottom" />

      {/* Fun and Trendy Variation */}
      <section className="h-screen bg-gradient-to-br from-purple-600 to-pink-500 relative">
        <SimpleNavbarFunAndTrendy
          leftButtonText="Menu"
          rightButtonText="Contact"
          onLeftButtonClick={() =>
            console.log("Fun & Trendy Left button clicked")
          }
          onRightButtonClick={() =>
            console.log("Fun & Trendy Right button clicked")
          }
        />
        <div className="h-full flex items-center justify-center">
          <div className="text-center text-white">
            <h1 className="text-6xl font-bold mb-4">Fun & Trendy</h1>
            <p className="text-xl">SimpleNavbar with retro styling</p>
          </div>
        </div>
      </section>

      {/* Futuristic Variation */}
      <section className="h-screen bg-gradient-to-br from-slate-900 to-blue-900 relative">
        <SimpleNavbarFuturistic
          leftButtonText="Menu"
          rightButtonText="Contact"
          onLeftButtonClick={() =>
            console.log("Futuristic Left button clicked")
          }
          onRightButtonClick={() =>
            console.log("Futuristic Right button clicked")
          }
        />
        <div className="h-full flex items-center justify-center">
          <div className="text-center text-white">
            <h1 className="text-6xl font-bold mb-4">Futuristic</h1>
            <p className="text-xl">SimpleNavbar with modern styling</p>
          </div>
        </div>
      </section>

      {/* Color Template Variations */}
      <section className="h-screen bg-gradient-to-br from-green-600 to-teal-500 relative">
        <SimpleNavbarFunAndTrendy
          colorTemplate={2}
          leftButtonText="Menu"
          rightButtonText="Contact"
          onLeftButtonClick={() =>
            console.log("Color Template 2 Left button clicked")
          }
          onRightButtonClick={() =>
            console.log("Color Template 2 Right button clicked")
          }
        />
        <div className="h-full flex items-center justify-center">
          <div className="text-center text-white">
            <h1 className="text-6xl font-bold mb-4">Color Template 2</h1>
            <p className="text-xl">Fun & Trendy with different colors</p>
          </div>
        </div>
      </section>
    </ReactLenis>
  );
}
